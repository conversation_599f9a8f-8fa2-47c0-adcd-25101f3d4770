package com.spdb.itoa.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.spdb.itoa.job.client.CustomDorisClient;
import com.spdb.itoa.job.client.ItoaRoutingDelegate;
import com.spdb.itoa.job.client.UqClient;
import com.spdb.itoa.job.client.model.SplResponseWrapper;
import com.spdb.itoa.job.common.constant.BasePage;
import com.spdb.itoa.job.common.constant.Paged;
import com.spdb.itoa.job.common.constant.address.AddressConst;
import com.spdb.itoa.job.common.constant.address.AddressEnum;
import com.spdb.itoa.job.common.exception.BizException;
import com.spdb.itoa.job.common.protocols.MsgCode;
import com.spdb.itoa.job.common.utils.*;
import com.spdb.itoa.job.entity.TbAddress;
import com.spdb.itoa.job.mapper.AddressMapper;
import com.spdb.itoa.job.model.address.*;
import com.spdb.itoa.job.service.AddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.spdb.itoa.job.common.constant.address.AddressConst.*;

@Slf4j
@Service
public class AddressServiceImpl extends ServiceImpl<AddressMapper, TbAddress> implements AddressService {
    @Autowired
    private CustomDorisClient customDorisClient;
    @Autowired
    private UqClient uqClient;
    @Autowired
    private DataSourceManager dataSourceManager;
    @Autowired
    private ItoaRoutingDelegate itoaRoutingDelegate;

    @Override
    public void createAddress(AddressDTO addressDTO) {
        //重名验证-任务名
        checkName(addressDTO);
        if (!addressDTO.getForce()) {
            //地址有效性检查
            checkAddress(addressDTO);
            //通过检查
            addressDTO.setStatus(AddressConst.ON);
        } else {
            //强制保存的都认为地址检查没通过,状态设置为失败
            addressDTO.setStatus(AddressConst.FAILURE);
        }
        addressDTO.setId(IdHelper.id());
        addressDTO.setUqClusterId(null);
        //其他业务处理-kafka 创建topic
        otherBusiness(addressDTO);
        //集群信息注册，或注册仓库
        registerCluster(addressDTO);
        //写入业务表
        TbAddress tbAddress = convertorDtoToDo(addressDTO);
        baseMapper.insert(tbAddress);
        FlexibleThreadPool.submitTask(() -> itoaRoutingDelegate.checkDorisEvent(new DorisEvent(CREATE, addressDTO.getDorisConfig())));
    }


    private void otherBusiness(AddressDTO addressDTO) {
        if (AddressEnum.DORIS.getCode().equals(addressDTO.getType())) {
            TbAddress address = baseMapper.selectById(addressDTO.getId());
            if (null == address) {
                initDorisDataBase(addressDTO.getDorisConfig());
            }
        }
    }

    private void initDorisDataBase(DorisConfig dorisConfig) {
        String sql = "set global describe_extend_variant_column = true;set global enable_variant_access_in_original_planner = true;";
        customDorisClient.executeSqlJdbc(dorisConfig, sql);
    }


    private void registerCluster(AddressDTO addressDTO) {
        AddressEnum addressEnum = AddressEnum.matchCode(addressDTO.getType());
        UqClusterSyncReq syncReq;
        if (Objects.requireNonNull(addressEnum) == AddressEnum.DORIS) {
            syncReq = parseDorisFrom(addressDTO.getDorisConfig(), addressDTO.getUqClusterId());
        } else {
            throw new BizException(MsgCode.NOT_MATCH, String.format("地址类型不匹配[%s]，请检查后重试！", addressEnum.getCode()));
        }
        //发送请求
        try {
            //注册集群
            if (syncReq.getClusterId() == null) {
                SplResponseWrapper<Long> resp = uqClient.addCluster(syncReq);
                if (resp.isSuccess()) {
                    addressDTO.setUqClusterId(resp.getData());
                } else {
                    log.error("syncCluster2Uq[UQ集群注册]-->error:UQ接口返回信息:{}", JsonUtil.toJsonPretty(resp));
                    throw new BizException(MsgCode.UPDATE_ERROR, "UQ集群注册失败，请联系管理员。");
                }
                //同步集群
            } else {
                SplResponseWrapper<Boolean> resp = uqClient.updateCluster(syncReq);
                if (!resp.isSuccess()) {
                    log.error("syncCluster2Uq[UQ集群修改]-->error:UQ接口返回信息:{}", JsonUtil.toJsonPretty(resp));
                    throw new BizException(MsgCode.UPDATE_ERROR, "UQ集群注册失败，请联系管理员。");
                }
            }
        } catch (Exception e) {
            log.error("syncCluster2Uq[UQ集群注册]-->error:UQ集群注册失败", e);
            throw new BizException(MsgCode.UPDATE_ERROR, "UQ集群注册失败，请联系管理员。");
        }
    }

    private UqClusterSyncReq parseDorisFrom(DorisConfig dorisConfig, Long uqClusterId) {
        String userName = dorisConfig.getSecurityConfig().getUsername();
        String password = dorisConfig.getSecurityConfig().getPassword();
        if (CollUtil.isNotEmpty(dorisConfig.getUserList())) {
            Optional<DorisUser> any = dorisConfig.getUserList().stream().filter(user -> "read".equals(user.getUseType())).findAny();
            if (any.isPresent()) {
                userName = any.get().getUsername();
                password = any.get().getPassword();
            }
        }
        return UqClusterSyncReq.builder()
                .source("aimeter")
                .clusterId(uqClusterId)
                .clusterInfo(UqClusterSyncReq.ClusterInfo.builder()
                        .name(dorisConfig.getClusterName())
                        .urls(dorisConfig.getJdbcUrl())
                        .user(userName)
                        .password(password)
                        .jdbcConfig(dorisConfig.getJdbcPropertiesMap())
                        .type("Doris")
                        .build())
                .build();
    }


    /**
     * 地址有效性检查
     *
     * @param addressDTO dto
     * @return 检查正常的消息
     */
    @Override
    public String checkAddress(AddressDTO addressDTO) {
        AddressEnum addressEnum = AddressEnum.matchCode(addressDTO.getType());
        if (Objects.requireNonNull(addressEnum) == AddressEnum.DORIS) {
            return checkDoris(addressDTO.getDorisConfig(), addressDTO.getId());
        }
        throw new BizException(MsgCode.NOT_MATCH, String.format("地址类型不匹配[%s]，请检查后重试！", addressEnum.getCode()));
    }

    private String checkDoris(DorisConfig dorisConfig, Long id) {
        //检查是否已存在相同的数据库及标签
        List<TbAddress> addressByType = getAddressByType(AddressEnum.DORIS.getCode());
        if (CollUtil.isNotEmpty(addressByType)) {
            for (TbAddress tbAddress : addressByType) {
                if (tbAddress.getId().equals(id)) {
                    continue;
                }
                DorisConfig config = JsonUtil.fromJson(tbAddress.getConfig(), DorisConfig.class);
                if (config.getDatabase().equals(dorisConfig.getDatabase()) && StrUtil.isNotEmpty(dorisConfig.getBeTag()) && StrUtil.isNotEmpty(config.getBeTag()) && dorisConfig.getBeTag().equals(config.getBeTag())) {
                    throw new BizException(MsgCode.VERIFY_ERROR, String.format("已存在相同的数据库[%s]及[%s]BE标签，请检查后重试！", dorisConfig.getDatabase(), dorisConfig.getBeTag()));
                }
            }
        }
        try {
            Map<String, Map<String, Object>> stringObjectMap = customDorisClient.checkCluster(dorisConfig);
            return String.format("验证成功，当前版本信息为：%s", stringObjectMap.getOrDefault("feVersionInfo", new HashMap<>()).getOrDefault("dorisBuildVersion", ""));
        } catch (Exception e) {
            log.error("checkDoris[doris集群检查]-->error:doris集群检查失败:", e);
            throw new BizException(MsgCode.VERIFY_ERROR, "验证失败，请检查后重试！");
        }
    }

    @Override
    public AddressVO getAddressByPlatform(String platformName) {
        if (StrUtil.isNotEmpty(platformName)) {
            List<AddressVO> addressVOList = listByType(AddressEnum.KAFKA.getCode());
            Optional<AddressVO> optionalAddressVO = addressVOList.stream()
                    .filter(addressVO -> {
                        if (CollUtil.isNotEmpty(addressVO.getKafkaConfig().getPlatform())) {
                            return addressVO.getKafkaConfig().getPlatform().stream().map(Platform::getName).toList().contains(platformName);
                        } else {
                            return false;
                        }
                    }).findFirst();
            if (optionalAddressVO.isPresent()) {
                return optionalAddressVO.get();
            }
        }
        return null;
    }

    @Override
    public void updateAddress(AddressDTO addressDTO) {
        //检查任务是否存在，如果存在则丰富一些数据，如uqs集群id
        TbAddress tbAddress = baseMapper.selectById(addressDTO.getId());
        addressDTO.setUqClusterId(tbAddress.getUqClusterId());
        //重名验证-任务名
        checkName(addressDTO);
        if (!addressDTO.getForce()) {
            //地址有效性检查
            checkAddress(addressDTO);
            //通过检查
            addressDTO.setStatus(AddressConst.ON);
        } else {
            //强制保存的都认为地址检查没通过,状态设置为失败
            addressDTO.setStatus(AddressConst.FAILURE);
        }
        //其他业务处理-kafka 创建topic
        otherBusiness(addressDTO);
        //集群信息注册，或注册仓库
        registerCluster(addressDTO);
        //写入业务表
        TbAddress address = convertorDtoToDo(addressDTO);
        baseMapper.updateById(address);
        FlexibleThreadPool.submitTask(() -> itoaRoutingDelegate.checkDorisEvent(new DorisEvent(UPDATE, addressDTO.getDorisConfig())));
    }

    @Override
    public void deleteAddress(Long id) {
        //检查地址是否在使用中
        TbAddress tbAddress = baseMapper.selectById(id);
        deleteClusterToUq(tbAddress.getUqClusterId());
        AddressVO addressVO = BeanConvertor.convert(tbAddress, AddressVO.class);
        convertConfig(addressVO, tbAddress.getConfig());
        baseMapper.deleteById(id);
        FlexibleThreadPool.submitTask(() -> itoaRoutingDelegate.checkDorisEvent(new DorisEvent(DELETE, addressVO.getDorisConfig())));
    }


    private void deleteClusterToUq(Long uqClusterId) {
        if (uqClusterId != null) {
            SplResponseWrapper<Boolean> resp = uqClient.deleteCluster(uqClusterId);
            if (resp.isSuccess() || resp.isWeakSuccess() || "0004".equals(resp.getErrorCode())) {
                return;
            }
            log.error("UQ集群删除失败，请联系管理员:{}", JsonUtil.toJson(resp));
            throw new BizException(MsgCode.DEL_ERROR, "UQ集群删除失败，请联系管理员。");
        }
    }

    @Override
    public AddressVO getAddress(Long id) {
        TbAddress address = baseMapper.selectById(id);
        if (null == address) {
            throw new BizException(MsgCode.NOT_EXIST, "地址不存在，请检查后重试");
        }
        AddressVO addressVO = BeanConvertor.convert(address, AddressVO.class);
        convertConfig(addressVO, address.getConfig());
        return addressVO;
    }

    @Override
    public DorisConfig getAddressByClusterName(String clusterName) {
        List<TbAddress> address = baseMapper.selectList(new LambdaQueryWrapper<TbAddress>()
                .eq(TbAddress::getType, AddressEnum.DORIS.getCode())
                .ne(TbAddress::getStatus, AddressConst.DELETED));
        if (CollUtil.isEmpty(address)) {
            throw new BizException(MsgCode.NOT_EXIST, "地址不存在，请检查后重试");
        }
        List<AddressVO> addressList = new ArrayList<>();
        for (TbAddress tbAddress : address) {
            AddressVO addressVO = BeanConvertor.convert(tbAddress, AddressVO.class);
            convertConfig(addressVO, tbAddress.getConfig());
            addressList.add(addressVO);
        }
        Optional<DorisConfig> any = addressList.stream().map(AddressVO::getDorisConfig).filter(x -> x.getClusterName().equals(clusterName)).findAny();
        if (any.isPresent()) {
            return any.get();
        }
        throw new BizException(MsgCode.NOT_EXIST, "地址不存在，请检查后重试");
    }

    @Override
    public List<DorisConfig> getAllDorisConfig() {
        List<TbAddress> address = baseMapper.selectList(new LambdaQueryWrapper<TbAddress>()
                .eq(TbAddress::getType, AddressEnum.DORIS.getCode())
                .ne(TbAddress::getStatus, AddressConst.DELETED));
        if (CollUtil.isEmpty(address)) {
            return Collections.emptyList();
        }
        List<AddressVO> addressList = new ArrayList<>();
        for (TbAddress tbAddress : address) {
            AddressVO addressVO = BeanConvertor.convert(tbAddress, AddressVO.class);
            convertConfig(addressVO, tbAddress.getConfig());
            addressList.add(addressVO);
        }
        return addressList.stream().map(AddressVO::getDorisConfig).toList();
    }

    @Override
    public void event(DorisEvent dorisEvent) {
        switch (dorisEvent.getEventType()) {
            case DELETE:
                dataSourceManager.closeAndRemoveDataSource(dorisEvent.getDorisConfig());
            case UPDATE:
            case CREATE:
                dataSourceManager.handleConfigUpdate(dorisEvent.getDorisConfig());
            default:
                throw new BizException(MsgCode.PARAMS_WARN, "未知的Doris事件" + dorisEvent.getEventType() + "请检查后重试！");
        }

    }

    @Override
    public Paged<AddressListVO> list(AddressListDTO addressListDTO) {
        Page<TbAddress> page = pageList(addressListDTO);
        List<Long> userIds = page.getRecords().stream().map(TbAddress::getUpdatedBy).toList();
//        Map<Long, String> usernames = userService.getDisplayNameByIds(userIds);

        List<AddressListVO> records = page.getRecords().stream().map(tbAddress -> {
            AddressListVO addressListVO = BeanConvertor.convert(tbAddress, AddressListVO.class);
//            addressListVO.setUpdateName(usernames.getOrDefault(tbAddress.getUpdatedBy(), ""));
            return addressListVO;
        }).collect(Collectors.toList());
        return new Paged<>(page.getTotal(), records);
    }

    public Page<TbAddress> pageList(AddressListDTO addressListDTO) {
        //分页参数
        Page<TbAddress> addressEntityPage = new Page<>(addressListDTO.getPage(), addressListDTO.getSize());
        BasePage.SortItem sort = addressListDTO.getFirstSort();
        SortUtil.initSort(addressEntityPage, sort.getKey(), sort.getOrder());
        return baseMapper.selectPage(addressEntityPage, new LambdaQueryWrapper<TbAddress>()
                .like(StrUtil.isNotEmpty(addressListDTO.getName()), TbAddress::getName, addressListDTO.getName())
                .eq(StrUtil.isNotEmpty(addressListDTO.getType()), TbAddress::getType, addressListDTO.getType())
                .ne(TbAddress::getStatus, AddressConst.DELETED));
    }

    @Override
    public List<AddressVO> listByType(String type) {
        List<TbAddress> addresses = getAddressByType(type);
        List<AddressVO> voList = new ArrayList<>();
        for (TbAddress tbAddress : addresses) {
            AddressVO vo = BeanConvertor.convert(tbAddress, AddressVO.class);
            convertConfig(vo, tbAddress.getConfig());
            voList.add(vo);
        }
        return voList;
    }

    public List<TbAddress> getAddressByType(String type) {
        return baseMapper.selectList(new LambdaQueryWrapper<TbAddress>()
                .eq(StrUtil.isNotEmpty(type), TbAddress::getType, type)
                .ne(TbAddress::getStatus, AddressConst.DELETED));
    }

    @Override
    public List<AddressVO> listByIds(List<Long> ids) {
        List<TbAddress> addresses = baseMapper.selectByIds(ids);
        List<AddressVO> voList = new ArrayList<>();
        for (TbAddress tbAddress : addresses) {
            AddressVO vo = BeanConvertor.convert(tbAddress, AddressVO.class);
            convertConfig(vo, tbAddress.getConfig());
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public Map<String, Object> getDorisInfo(DorisConfig dorisConfig) {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("database", customDorisClient.getDorisDatabase(dorisConfig));
            result.put("clusterInfo", customDorisClient.getClusterInfo(dorisConfig));
            result.put("beNodeTag", customDorisClient.getDorisBeNodeLab(dorisConfig));
            return result;
        } catch (Exception ex) {
            log.error("获取doris信息失败", ex);
            throw new BizException(MsgCode.CONNECT_EXCEPTION, "获取doris信息失败，请检查后重试！");
        }
    }

    @Override
    public Integer getDorisBeNodeNum(Long id) {
        TbAddress address = baseMapper.selectById(id);
        if (address == null) {
            throw new BizException(MsgCode.NOT_EXIST, "地址信息不存在，请检查后重试！");
        }
        AddressVO addressVO = BeanConvertor.convert(address, AddressVO.class);
        convertConfig(addressVO, address.getConfig());
        return getDorisBeNodesByTag(addressVO.getDorisConfig()).size();
    }

    @Override
    public List<DorisBeNode> getDorisBeNodesByTag(DorisConfig dorisConfig) {
        List<DorisBeNode> beNodes = customDorisClient.getDorisBeNodesByTag(dorisConfig);
        if (CollUtil.isNotEmpty(beNodes)) {
            return beNodes;
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getDorisBeNodes(DorisConfig dorisConfig) {
        List<DorisBeNode> dorisBeNodesByTag = customDorisClient.getDorisBeNodesByTag(dorisConfig);
        return dorisBeNodesByTag.stream().map(x -> x.getHost() + ":" + x.getHttpPort()).distinct().collect(Collectors.toList());
    }

    private void checkName(AddressDTO addressDTO) {
        Long count = getAddressCountByName(addressDTO.getName(), addressDTO.getId());
        //转换成TbAddressDTO
        if (count > 0) {
            throw new BizException(MsgCode.ALREADY_EXIST, String.format("地址名称[%s]已存在，请检查后重试！", addressDTO.getName()));
        }
    }

    public Long getAddressCountByName(String name, Long id) {
        return baseMapper.selectCount(Wrappers.lambdaQuery(TbAddress.class)
                .eq(StrUtil.isNotEmpty(name), TbAddress::getName, name)
                .ne(null != id && id > 0, TbAddress::getId, id)
                .ne(TbAddress::getStatus, AddressConst.DELETED));
    }


    private void convertConfig(AddressVO vo, String config) {
        AddressEnum addressEnum = AddressEnum.matchCode(vo.getType());
        if (Objects.requireNonNull(addressEnum) == AddressEnum.DORIS) {
            vo.setDorisConfig(JsonUtil.fromJson(config, DorisConfig.class));
            return;
        }
        throw new BizException(MsgCode.NOT_MATCH, String.format("地址类型不匹配[%s]，请检查后重试！", addressEnum.getCode()));
    }

    private TbAddress convertorDtoToDo(AddressDTO addressDTO) {
        TbAddress tbAddress = BeanConvertor.convert(addressDTO, TbAddress.class);
        AddressEnum addressEnum = AddressEnum.matchCode(addressDTO.getType());
        if (Objects.requireNonNull(addressEnum) == AddressEnum.DORIS) {
            tbAddress.setConfig(JsonUtil.toJson(addressDTO.getDorisConfig()));
        } else {
            throw new BizException(MsgCode.NOT_MATCH, String.format("地址类型不匹配[%s]，请检查后重试！", addressEnum.getCode()));
        }
        return tbAddress;
    }

    private String getDataId(Long id) {
        return "doris_" + id;
    }
}
