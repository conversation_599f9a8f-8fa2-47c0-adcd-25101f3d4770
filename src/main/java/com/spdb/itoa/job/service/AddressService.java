package com.spdb.itoa.job.service;

import com.spdb.itoa.job.common.constant.Paged;
import com.spdb.itoa.job.model.address.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface AddressService {
    /**
     * 创建地址
     *
     * @param addressDTO 地址对象
     */
    @Transactional(rollbackFor = Exception.class)
    void createAddress(AddressDTO addressDTO);

    /**
     * 地址验证
     *
     * @param addressDTO 地址对象模型
     * @return message
     */
    String checkAddress(AddressDTO addressDTO);

    /**
     * 修改地址
     *
     * @param addressDTO 地址对象
     */
    @Transactional(rollbackFor = Exception.class)
    void updateAddress(AddressDTO addressDTO);

    /**
     * 根据平台名读取地址信息
     *
     * @param platformName 平台名称
     * @return AddressVO
     */
    AddressVO getAddressByPlatform(String platformName);

    /**
     * 删除地址
     *
     * @param id 地址id
     */
    void deleteAddress(Long id);

    /**
     * 获取地址详情
     *
     * @param id 地址id
     * @return AddressVO
     */
    AddressVO getAddress(Long id);

    /**
     * 读取地址列表
     *
     * @param addressListDTO 分页对象
     * @return PageVO<AddressListVO>
     */
    Paged<AddressListVO> list(AddressListDTO addressListDTO);

    /**
     * 根据类型读取地址列表
     *
     * @param type 地址类型
     * @return 地址列表
     */
    List<AddressVO> listByType(String type);


    /**
     * 根据id 批量获取地址信息
     *
     * @param ids id列表
     * @return List<AddressVO>
     */
    List<AddressVO> listByIds(List<Long> ids);


    /**
     * 获取doris基础信息
     *
     * @param dorisConfig 配置信息
     * @return doris基础信息
     */
    Map<String, Object> getDorisInfo(DorisConfig dorisConfig);

    /**
     * 获取doris be 节点信息
     *
     * @param id id
     * @return doris be 节点数量
     */
    Integer getDorisBeNodeNum(Long id);

    /**
     * 根据标签获取be节点信息
     *
     * @param dorisConfig config
     * @return List<DorisBeNode>
     */
    List<DorisBeNode> getDorisBeNodesByTag(DorisConfig dorisConfig);

    /**
     * 获取doris be节点信息
     *
     * @param dorisConfig dorisConfig
     * @return List<String>
     */
    List<String> getDorisBeNodes(DorisConfig dorisConfig);

    /**
     * 根据集群名读取doris 信息
     *
     * @param clusterName 集群名
     * @return DorisConfig
     */
    DorisConfig getAddressByClusterName(String clusterName);

    /**
     * 获取所有Nacos地址
     *
     * @return List<DorisConfig>
     */
    List<DorisConfig> getAllDorisConfig();

    /**
     * doris 事件触发
     *
     * @param dorisEvent DorisEvent
     */
    void event(DorisEvent dorisEvent);
}
