package com.spdb.itoa.job.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.spdb.itoa.job.common.constant.Paged;
import com.spdb.itoa.job.common.context.Context;
import com.spdb.itoa.job.common.exception.BizException;
import com.spdb.itoa.job.common.protocols.MsgCode;
import com.spdb.itoa.job.common.utils.IdHelper;
import com.spdb.itoa.job.entity.TbCluster;
import com.spdb.itoa.job.mapper.ClusterMapper;
import com.spdb.itoa.job.model.cluster.ClusterQuery;
import com.spdb.itoa.job.model.cluster.ClusterReq;
import com.spdb.itoa.job.model.cluster.ClusterResp;
import com.spdb.itoa.job.service.ClusterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ClusterServiceImpl implements ClusterService {

    @Autowired
    private ClusterMapper clusterMapper;

    @Override
    public Paged<ClusterResp> query(ClusterQuery req) {
        IPage<TbCluster> page = clusterMapper.selectPage(req.page(), req.wrapper());
        List<ClusterResp> records = page.getRecords().stream()
                .map(x -> new ClusterResp().fromEntity(x)).toList();
        return new Paged<ClusterResp>(page.getTotal(), records);
    }

    @Override
    public List<ClusterResp> list() {
        List<TbCluster> list = clusterMapper.selectList(null);
        return list.stream().map(x -> new ClusterResp().fromEntity(x)).toList();
    }

    @Override
    public ClusterResp get(Long id) {
        TbCluster entity = clusterMapper.selectById(id,
                new BizException(MsgCode.NOT_EXIST, String.format("集群不存在[%s]", id)));
        return new ClusterResp().fromEntity(entity);
    }

    @Override
    public ClusterResp create(ClusterReq req) {
        log.info("create cluster req: {}", req);
        long now = System.currentTimeMillis();
        long userId = Context.getUserId();
        TbCluster entity = req.toEntity(new TbCluster());
        entity.setId(IdHelper.id());
        entity.setCreated(now);
        entity.setCreatedBy(userId);
        entity.setUpdated(now);
        entity.setUpdatedBy(userId);
        clusterMapper.insert(entity);
        log.info("create cluster resp: {}", entity);
        return new ClusterResp().fromEntity(entity);
    }

    @Override
    public ClusterResp update(ClusterReq req) {
        log.info("update cluster req: {}", req);
        long now = System.currentTimeMillis();
        long userId = Context.getUserId();
        Long id = req.getId();
        TbCluster entity = clusterMapper.selectById(id,
                new BizException(MsgCode.NOT_EXIST, String.format("集群不存在[%s]", id)));
        req.toEntity(entity);
        entity.setUpdated(now);
        entity.setUpdatedBy(userId);
        clusterMapper.updateById(entity);
        log.info("update cluster resp: {}", entity);
        return new ClusterResp().fromEntity(entity);
    }

    @Override
    public ClusterResp delete(Long id) {
        log.info("delete cluster id: {}", id);
        TbCluster entity = clusterMapper.selectById(id,
                new BizException(MsgCode.NOT_EXIST, String.format("集群不存在[%s]", id)));
        clusterMapper.deleteById(id);
        log.info("delete cluster resp: {}", entity);
        return new ClusterResp().fromEntity(entity);
    }
}
