package com.spdb.itoa.job.service;

import com.spdb.itoa.job.common.constant.Paged;
import com.spdb.itoa.job.model.cluster.ClusterQuery;
import com.spdb.itoa.job.model.cluster.ClusterReq;
import com.spdb.itoa.job.model.cluster.ClusterResp;

import java.util.List;

public interface ClusterService {

    Paged<ClusterResp> query(ClusterQuery req);

    List<ClusterResp> list();

    ClusterResp get(Long id);

    ClusterResp create(ClusterReq req);

    ClusterResp update(ClusterReq req);

    ClusterResp delete(Long id);
}
