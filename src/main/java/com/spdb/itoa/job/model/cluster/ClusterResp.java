package com.spdb.itoa.job.model.cluster;

import com.fasterxml.jackson.core.type.TypeReference;
import com.spdb.itoa.job.common.utils.BeanConvertor;
import com.spdb.itoa.job.common.utils.JsonUtil;
import com.spdb.itoa.job.entity.TbCluster;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@Data
@ToString
public class ClusterResp {

    private Long id;

    /**
     * 集群名称
     */
    private String name;

    /**
     * 集群类型,hadoop,flink
     */
    private String type;

    /**
     * 备注信息
     */
    private String description;

    /**
     * 高级配置(json)
     */
    private Map<String, Object> setting;

    /**
     * 创建时间
     */
    private Long created;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 修改时间
     */
    private Long updated;

    /**
     * 修改人
     */
    private Long updatedBy;

    public ClusterResp fromEntity(TbCluster entity) {
        BeanConvertor.convert(entity, this);
        this.setting = JsonUtil.toMap(entity.getSetting());
        return this;
    }
}
