package com.spdb.itoa.job.model.cluster;

import com.spdb.itoa.job.common.utils.BeanConvertor;
import com.spdb.itoa.job.common.utils.JsonUtil;
import com.spdb.itoa.job.entity.TbCluster;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@Data
@ToString
public class ClusterReq {

    private Long id;

    /**
     * 集群名称
     */
    private String name;

    /**
     * 集群类型,hadoop,flink
     */
    private String type;

    /**
     * 备注信息
     */
    private String description;

    /**
     * 高级配置(json)
     */
    private Map<String, Object> setting;

    public TbCluster toEntity(TbCluster entity) {
        BeanConvertor.convert(this, entity);
        entity.setSetting(JsonUtil.toJson(setting));
        return entity;
    }
}
