package com.spdb.itoa.job.model.cluster;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.spdb.itoa.job.common.constant.BaseQuery;
import com.spdb.itoa.job.entity.TbCluster;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ClusterQuery extends BaseQuery<TbCluster> {
    private String name;
    private String type;
    private String description;

    public QueryWrapper<TbCluster> where(QueryWrapper<TbCluster> wrapper) {
        wrapper.lambda().like(StrUtil.isNotEmpty(name), TbCluster::getName, name)
                .eq(StrUtil.isNotEmpty(type), TbCluster::getType, type)
                .like(StrUtil.isNotEmpty(description), TbCluster::getDescription, description);
        return wrapper;
    }
}
