package com.spdb.itoa.job.controller.cluster;

import com.spdb.itoa.job.common.constant.Paged;
import com.spdb.itoa.job.common.constant.Result;
import com.spdb.itoa.job.model.cluster.ClusterQuery;
import com.spdb.itoa.job.model.cluster.ClusterReq;
import com.spdb.itoa.job.model.cluster.ClusterResp;
import com.spdb.itoa.job.service.ClusterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "集群管理", description = "集群管理")
@RestController
@RequestMapping("/api/v1/cluster")
public class ClusterController {
    @Autowired
    private ClusterService clusterService;

    @PostMapping("/page")
    @Operation(summary = "分页查询集群")
    public Result<Paged<ClusterResp>> query(@RequestBody ClusterQuery req) {
        return Result.success(clusterService.query(req));
    }

    @GetMapping("")
    @Operation(summary = "集群列表")
    public Result<List<ClusterResp>> list() {
        return Result.success(clusterService.list());
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取集群")
    public Result<ClusterResp> get(@PathVariable Long id) {
        return Result.success(clusterService.get(id));
    }

    @PostMapping("")
    @Operation(summary = "创建集群")
    public Result<ClusterResp> create(@RequestBody ClusterReq req) {
        return Result.success(clusterService.create(req));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改集群")
    public Result<ClusterResp> update(@PathVariable Long id, @RequestBody ClusterReq req) {
        req.setId(id);
        return Result.success(clusterService.update(req));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除集群")
    public Result<ClusterResp> delete(@PathVariable Long id) {
        return Result.success(clusterService.delete(id));
    }
}
