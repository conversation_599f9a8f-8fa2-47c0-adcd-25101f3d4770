package com.spdb.itoa.job.controller.address;

import com.spdb.itoa.job.common.constant.Paged;
import com.spdb.itoa.job.common.constant.Result;
import com.spdb.itoa.job.model.address.*;
import com.spdb.itoa.job.service.AddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Size;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 地址管理控制器
 *
 * <AUTHOR>
 * 2025年05月27日10:05:16
 */
@Tag(name = "地址管理", description = "地址管理")
@RestController
@RequestMapping("/api/v1/address")
@Validated
public class AddressController {
    @Autowired
    private AddressService addressService;

    @PostMapping("")
    @Operation(summary = "创建地址")
    public Result<Object> createAddress(@RequestBody AddressDTO addressDTO) {
        addressService.createAddress(addressDTO);
        return Result.success(null, "保存成功！");
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除地址")
    public Result<Object> deleteAddress(@PathVariable Long id) {
        addressService.deleteAddress(id);
        return Result.success(null, "删除成功！");
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改地址")
    public Result<Object> updateAddress(@PathVariable Long id, @RequestBody AddressDTO addressDTO) {
        addressDTO.setId(id);
        addressService.updateAddress(addressDTO);
        return Result.success(null, "修改成功！");
    }


    @GetMapping("/{id}")
    @Operation(summary = "获取地址详情")
    public Result<AddressVO> getAddress(@PathVariable Long id) {
        return Result.success(addressService.getAddress(id));
    }

    @PostMapping("/list")
    @Operation(summary = "获取地址列表")
    public Result<Paged<AddressListVO>> list(@RequestBody AddressListDTO addressListDTO) {
        return Result.success(addressService.list(addressListDTO));
    }

    @GetMapping("/list")
    @Operation(summary = "根据地址类型获取地址列表")
    public Result<List<AddressVO>> listByType(@RequestParam("type") String type) {
        return Result.success(addressService.listByType(type));
    }

    @PostMapping("/check")
    @Operation(summary = "地址检查")
    public Result<String> check(@RequestBody AddressDTO addressDTO) {
        return Result.success(null, addressService.checkAddress(addressDTO));
    }

    @Operation(summary = "获取doris基础信息")
    @PostMapping("/doris-info")
    public Result<Map<String, Object>> getDorisInfo(@RequestBody DorisConfig dorisConfig) {
        return Result.success(addressService.getDorisInfo(dorisConfig));
    }

    @Operation(summary = "获取doris-BE的数量信息")
    @GetMapping("/doris-be-node-num/{id}")
    public Result<Integer> getDorisBeNodeNum(@PathVariable("id") Long id) {
        return Result.success(addressService.getDorisBeNodeNum(id));
    }


    @Operation(summary = "根据平台名称读取地址信息")
    @GetMapping("/address-platform/")
    public Result<AddressVO> getAddressByPlatform(@RequestParam("platformName") String platformName) {
        return Result.success(addressService.getAddressByPlatform(platformName));
    }

    @Operation(summary = "根据地址id 批量读取地址信息")
    @PostMapping("/list/by-ids")
    public Result<List<AddressVO>> listByIds(@RequestBody
                                             @Size(min = 1, max = 1000, message = "地址id列表长度有误，最小为1，最大为1000！")
                                             List<Long> ids) {
        return Result.success(addressService.listByIds(ids));
    }

    @Operation(summary = "doris 事件下发")
    @PostMapping("/event")
    public Result<Object> event(@RequestBody DorisEvent dorisEvent) {
        addressService.event(dorisEvent);
        return Result.success();
    }
}
