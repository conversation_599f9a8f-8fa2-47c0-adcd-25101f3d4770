package com.spdb.itoa.job.common.constant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.ArrayList;
import java.util.List;

/**
 * 基础分页
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "基础分页")
public class BasePage {
    @NotNull(message = "分页参数不可为空")
    @Schema(description = "分页大小", example = "10")
    private Integer size = 10;

    @Range(message = "页数有误，页数不能小于0，请检查后重试！")
    @Schema(description = "当前第几页", example = "1")
    private Integer page = 1;

    @Valid
    @Schema(description = "排序")
    private List<SortItem> sort = new ArrayList<>();

    /**
     * 默认返回第一个排序规则
     *
     * @return SortItem
     */
    @JsonIgnore
    public SortItem getFirstSort() {
        return getFirstSort(new SortItem());
    }

    /**
     * 默认返回第一个排序规则
     *
     * @return
     */
    @JsonIgnore
    public SortItem getFirstSort(SortItem defaultSort) {
        if (CollUtil.isEmpty(sort)) {
            return defaultSort;
        }
        return sort.get(0);
    }

    /**
     * 前端默认从1开始算第一页
     *
     * @return Integer
     */
    @JsonIgnore
    public Integer getAdaptivePage() {
        if (page == null) {
            return 1;
        }
        return page;
    }

    public Integer getSize() {
        if (size == null) {
            return Integer.MAX_VALUE;
        }
        return size;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SortItem {

        @Schema(description = "排序字段", example = "updated")
        private String key = "updated";

        @Schema(description = "排序顺序", example = "desc")
        private String order = "desc";

        public boolean isAsc() {
            return "asc".equalsIgnoreCase(order);
        }
    }

}

