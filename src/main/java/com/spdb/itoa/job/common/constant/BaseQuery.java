package com.spdb.itoa.job.common.constant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class BaseQuery<T> extends BasePage {

    public abstract QueryWrapper<T> where(QueryWrapper<T> wrapper);

    public QueryWrapper<T> order(QueryWrapper<T> wrapper) {
        if (CollUtil.isNotEmpty(getSort())) {
            Class<?> entityClass = ClassUtil.getTypeArgument(this.getClass());
            TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
            List<TableFieldInfo> fieldList = tableInfo.getFieldList();
            Map<String, String> fieldMap = new HashMap<>(fieldList.size());
            for (TableFieldInfo fieldInfo : fieldList) {
                fieldMap.put(fieldInfo.getProperty(), fieldInfo.getColumn());
            }
            for (SortItem sortItem : getSort()) {
                String column = fieldMap.get(sortItem.getKey());
                wrapper.orderBy(StrUtil.isNotEmpty(column), sortItem.isAsc(), column);
            }
        }
        if (getSort().stream().noneMatch(i -> "updated".equals(i.getKey()))) {
            wrapper.orderByDesc("updated");
        }
        return wrapper;
    }

    public Page<T> page() {
        return new Page<>(getPage(), getSize());
    }

    public QueryWrapper<T> wrapper() {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        where(wrapper);
        order(wrapper);
        return wrapper;
    }
}
