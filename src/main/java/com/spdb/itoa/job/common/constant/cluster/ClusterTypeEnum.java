package com.spdb.itoa.job.common.constant.cluster;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum ClusterTypeEnum {
    YARN("yarn"),
    FLINK("flink"),
    ;

    private String code;

    public static ClusterTypeEnum matchCode(String code) {
        ClusterTypeEnum result = null;
        for (ClusterTypeEnum clusterTypeEnum : values()) {
            if (clusterTypeEnum.getCode().equals(code)) {
                result = clusterTypeEnum;
                break;
            }
        }
        return result;
    }
}
